@{
    Layout = null;
    var v = "2019-02-19";
}
@using MT.Enterprise.Product.IdentityServer.Models
@model LoginResultDto
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ME-Boost Passport</title>
    <link href="../css/reset.css?_=@v" rel="stylesheet">
    <link href="../css/login.css?_=@v" rel="stylesheet">
</head>

<body>
<div id="particles">
    @if (!string.IsNullOrEmpty(Model.AccessToken) && Model.ExpiresIn > 0)
    {
        <div class="login-form">
            <h3 class="login-form-title" id="loggingin">
            </h3>
        </div>
    }
    <p class="error-message"></p>
</div>
    <script src="../lib/axios.min.js?_=@v"></script>
    <script src="../lib/particles.js?_=@v"></script>
    <script>
        var accessToken = '@Model.AccessToken';
        var expiresIn = @Model.ExpiresIn;
    </script>
    <script src="../js/login.js?_=@v"></script>
    <script src="../js/p.js?_=@v"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.13.42/dingtalk.open.js"></script>
    @if (string.IsNullOrEmpty(Model.AccessToken) || Model.ExpiresIn == 0)
    {
        <script type="text/javascript">
            // 获取参数
            function getQueryStringByName(name) {
                var result = location.search.match(new RegExp("[\?\&]" + name + "=([^\&]+)", "i"));
                if (result == null || result.length < 1) {
                    return "";
                }
                return result[1];
            }

            window.onload = function() {
                dd.ready(function() {
                    // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
                    dd.runtime.permission.requestAuthCode({
                        corpId: getQueryStringByName("corpid"),
                        onSuccess: function(res) {
                            alert(JSON.stringify(res));
                            alert(res.code);
                            if (res && res.code) {
                                axios
                                    .post('/ddaccount/login', {
                                        code: res.code
                                    })
                                    .then(function(response) {
                                        alert(JSON.stringify(response));
                                        postback(response.data.accessToken, response.data.expiresIn);
                                    })
                                    .catch(function(error) {
                                        var message;
                                        if (error.response && error.response.status === 400) {
                                            message = Locale[currentLang].error.notfound;
                                        } else {
                                            message = (error.response && error.response.data) || error;
                                        }
                                        document.querySelector('.error-message').textContent = message;
                                    });
                            }
                        },
                        onFail : function(err) {}
  
                    });
                });
            };
        </script>
    }
</body>

</html>
