@{
    Layout = null;
    var v = "2022-09-08";
}
@using MT.Enterprise.Product.IdentityServer.Models
@model LoginResultDto
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ME-Boost Passport</title>
    <link href="../css/reset.css?_=@v" rel="stylesheet">
    <link href="../css/login.css?_=@v" rel="stylesheet">
</head>
<body>
    <script src="../lib/axios.min.js?_=@v"></script>
    <script>
        var accessToken = '@Model.AccessToken';
        var expiresIn = @Model.ExpiresIn;
    </script>
    <script src="../js/caslogin.js?_=@v"></script>
</body>
</html>