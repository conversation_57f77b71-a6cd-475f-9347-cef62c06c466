using System;
using System.Threading.Tasks;
using IdentityModel.Client;
using Microsoft.AspNetCore.Mvc;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// HomeController
    /// </summary>
    [Route("movitidentity/home")]
    public class HomeController : BaseController
    {
        private readonly string _urlKey;

        /// <summary>
        /// HomeController
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public HomeController(IServiceProvider serviceProvider)
            : base(serviceProvider)
        {
            _urlKey = P_appSettings["UrlKey"]?.ToString();
        }

        /// <summary>
        /// Index
        /// </summary>
        /// <returns>首页</returns>
        [HttpGet("index")]
        public IActionResult Index()
        {
            Console.WriteLine($"时间====>{DateTime.Now}");
            ViewBag.UrlKey = string.IsNullOrEmpty(_urlKey) ? string.Empty : "/" + _urlKey;
            return View();
        }

        /// <summary>
        /// test
        /// </summary>
        [HttpGet("test")]
        public void Test()
        {
            LogInformation("获取Test：" + DateTime.Now);
            /*var doc = await GetDiscovery();
            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");
            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "account", UserName = "test" });*/
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        [NonAction]
        async Task<DiscoveryResponse> GetDiscovery()
        {
            var client = new DiscoveryClient($"http://{Request.Host.Value}");
            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}
