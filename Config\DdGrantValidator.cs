﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityModel;
using IdentityServer4.Models;
using IdentityServer4.Validation;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Config
{
    /// <summary>
    /// 钉钉验证
    /// </summary>
    public class DdGrantValidator : IExtensionGrantValidator
    {
        private readonly IDbContext _dbContext;
        private readonly JObject _appSettings;
        private readonly IMemoryCache _memoryCache;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public DdGrantValidator(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<IDbContext>();
            _appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _memoryCache = serviceProvider.GetService<IMemoryCache>();
        }

        /// <summary>
        /// 类型
        /// </summary>
        public string GrantType => "Dd";

        /// <summary>
        /// 验证
        /// </summary>
        /// <param name="context">context</param>
        /// <returns>returns</returns>
        public async Task ValidateAsync(ExtensionGrantValidationContext context)
        {
            var user = await GetUserLoginIdAsync(context.Request.Raw["Code"]);
            if (user == null)
            {
                throw new System.Exception($"根据钉钉Code：{context.Request.Raw["Code"]}没有获取到有效用户");
            }

            if (!_memoryCache.TryGetValue(user.UserLoginId.ToLower(), out User _))
            {
                _memoryCache.Set(user.UserLoginId.ToLower(), user, TimeSpan.FromSeconds(15));
            }

            context.Result = new GrantValidationResult(user.UserLoginId, OidcConstants.AuthenticationMethods.Password);
        }

        /// <summary>
        /// 获取用户登录id
        /// </summary>
        /// <param name="code">code</param>
        /// <returns>returns</returns>
        public async Task<User> GetUserLoginIdAsync(string code)
        {
            // 钉钉code放在username传过来
            if (string.IsNullOrWhiteSpace(code))
            {
                return null;
            }

            #region 根据钉钉code 到钉钉api获取工号

            HttpContent content = new StringContent($"code={code}");
            content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
            using var client = new HttpClient();
            var resp = await client.PostAsync($"{_appSettings["DdSettings"]?["ApiUrl"]}/topapi/v2/user/getuserinfo?access_token={await GetTokenAsync()}", content);
            if (!resp.IsSuccessStatusCode)
            {
                return null;
            }

            var respStr = resp.Content.ReadAsStringAsync().Result;

            var jObject = Newtonsoft.Json.JsonConvert.DeserializeObject<JObject>(respStr);
            if (jObject == null || jObject["errcode"]?.ToString() != "0" || string.IsNullOrWhiteSpace(jObject["result"]?["userid"]?.ToString()))
            {
                return null;
            }

            #endregion

            return await _dbContext.Client.Queryable<User>().FirstAsync(it => it.WorkNumber == jObject["result"]["userid"].ToString() && it.Status == 1);
        }

        /// <summary>
        /// 获取钉钉Token
        /// </summary>
        /// <returns>returns</returns>
        public async Task<string> GetTokenAsync()
        {
            // 从缓存中获取,如果没有调用钉钉api获取并缓存起来
            if (_memoryCache.TryGetValue("DdToken", out string token))
            {
                return token;
            }

            using var client = new HttpClient();
            var respStr = await client.GetStringAsync($"{_appSettings["DdSettings"]?["ApiUrl"]}/gettoken?appkey={_appSettings["DdSettings"]?["AppKey"]}&appsecret={_appSettings["DdSettings"]?["AppSecret"]}");
            if (string.IsNullOrWhiteSpace(respStr))
            {
                return string.Empty;
            }

            var jObject = Newtonsoft.Json.JsonConvert.DeserializeObject<JObject>(respStr);
            if (jObject == null || jObject["errcode"]?.ToString() != "0")
            {
                return string.Empty;
            }

            _memoryCache.Set("DdToken", jObject["access_token"]?.ToString(), TimeSpan.FromSeconds(int.Parse(jObject["expires_in"]?.ToString() ?? "600") - 10));
            return jObject["access_token"]?.ToString();
        }
    }
}
