using System;
using System.IO;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using MT.Enterprise.Core;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.Log;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Config;
using Nacos.V2;
using Nacos.V2.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer
{
    /// <summary>
    /// Startup.
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration">Configuration</param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// Gets 获取配置
        /// </summary>
        /// <value>The configuration.</value>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// 配置文件
        /// </summary>
        public JObject AppConfigDto
        {
            get;
            set;
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        /// </summary>
        /// <param name="services">Services.</param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMemoryCache();
            services.AddControllersWithViews();

            var nacosConfig = Configuration.GetSection("Nacos");

            services.AddNacosV2Config(Configuration);

            AppConfigDto = CreateRegister(services, nacosConfig);

            services.AddCore(core =>
            {
                core.AddORM(orm =>
                {
                    foreach (var dbConfig in AppConfigDto["Persistence"] !.Children())
                    {
                        string dbName = ((JProperty)dbConfig).Name;
                        Enum.TryParse(dbConfig.First()["DbType"]?.ToString(), true, out ConnectionDbType dbType);
                        orm.AddDbContext(opt =>
                        {
                            opt.Name = dbName;
                            opt.ConnectionString = dbConfig.First()["ConnectionString"]?.ToString(); // dbConfig["ConnectionString"];
                            opt.ConnectionDbType = dbType;
                            opt.IsDefault = true;
                        });
                    }
                });

                Enum.TryParse(AppConfigDto["LogSettings"]?["QueryDbType"]?.ToString(), true, out PersistentType logDbType);
                core.AddLog(log =>
                {
                    log.MinimumLevel =
                        NLog.LogLevel.AllLevels.ToList()[
                            Convert.ToInt32(AppConfigDto["LogSettings"]?["MinimumLevel"]?.ToString())];
                    log.AddTarget(opt =>
                    {
                        opt.Type = logDbType;
                        switch (opt.Type)
                        {
                            case PersistentType.Exceptionless:
                                opt.ApiKey = AppConfigDto["LogSettings"]?["ApiKey"]?.ToString();
                                opt.ServerUrl = AppConfigDto["LogSettings"]?["ServerUrl"]?.ToString();
                                break;
                            default:
                                opt.LogAddress = AppConfigDto["LogSettings"]?["LogAddress"]?.ToString();
                                break;
                        }
                    });
                });
            });

            services.AddIdentityServer()
                    .AddDeveloperSigningCredential()
                    .AddInMemoryIdentityResources(IdentityResourcesStore.Get())
                    .AddInMemoryClients(ClientsStore.Get())
                    .AddProfileService<ProfileService>()
                    .AddExtensionGrantValidator<ExtensionGrantValidator>();

            // .AddResourceOwnerValidator<ResourceOwnerPasswordValidator>()
            services.AddAuthentication("ME-Boost").AddCookie("ME-Boost", options =>
            {
                // 与 AccessTokenLifetime 保持一致
                options.ExpireTimeSpan = TimeSpan.FromSeconds(3600);
            });
        }

        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app">App.</param>
        /// <param name="env">Env.</param>
        /// <param name="cache">cache.</param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IMemoryCache cache)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseErrorHandling(true);
            }

            var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
            cache.Set("AppSettings", AppConfigDto, entryOptions);

            app.UseIdentityServer();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();

            // 解决非https不能跨域写cookies的问题，https需要去掉
            if (!AppConfigDto["RequestScheme"] !.ToString().ToLower().Equals("https"))
            {
                app.UseCookiePolicy(new CookiePolicyOptions { MinimumSameSitePolicy = SameSiteMode.Lax });
            }

            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(
                Path.Combine(Directory.GetCurrentDirectory(), @"wwwroot/assets")),
                RequestPath = new PathString("/movitidentity")
            });

            app.UseEndpoints(endpoints => endpoints.MapControllers());
        }

        private JObject CreateRegister(IServiceCollection services, IConfigurationSection section)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                string config = string.Empty;
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }

                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigService>();

                var res = configClient.GetConfig(section["DataId"], section["GroupId"], 3000).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }
    }
}
