using System.Collections.Generic;
using System.Linq;
using IdentityServer4;
using IdentityServer4.Models;

namespace MT.Enterprise.Product.IdentityServer.Config
{
    /// <summary>
    /// ClientsStore
    /// </summary>
    public static class ClientsStore
    {
        /// <summary>
        /// Get
        /// </summary>
        /// <returns>Client</returns>
        public static IEnumerable<Client> Get()
        {
            return new List<Client>
            {
                new Client
                {
                    ClientId = "client",
                    AllowedGrantTypes = GrantTypes.ResourceOwnerPassword.Union(new List<string>() { "Movitech" }).ToList(),

                    ClientSecrets =
                    {
                        new Secret("me-boost".Sha256())
                    },
                    AllowedScopes =
                    {
                        IdentityServerConstants.StandardScopes.OpenId,
                        IdentityServerConstants.StandardScopes.Profile
                    },

                    // AccessToken 有效期
                    AccessTokenLifetime = 3600,

                    // Jwt 无法注销，所以必须使用 Reference Token
                    AccessTokenType = AccessTokenType.Reference
                }
            };
        }
    }
}
