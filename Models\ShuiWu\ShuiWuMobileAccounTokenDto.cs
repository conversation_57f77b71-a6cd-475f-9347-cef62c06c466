﻿namespace MT.Enterprise.Product.IdentityServer.Models.ShuiWu
{
    /// <summary>
    /// 手机端tokeninfo
    /// </summary>
    public class ShuiWuMobileAccounTokenDto
    {
        /// <summary>
        /// code
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 获取用户数据成功
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Result 默认值: true
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 返回用户数据.
        /// </summary>
        public DataDto Data { get; set; }

        /// <summary>
        /// 用户data
        /// </summary>
        public class DataDto
        {
            /// <summary>
            /// Id
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// Username
            /// </summary>
            public string Username { get; set; }

            /// <summary>
            /// UserType
            /// </summary>
            public string UserType { get; set; }

            /// <summary>
            /// Phone
            /// </summary>
            public string Phone { get; set; }
        }
    }
}
