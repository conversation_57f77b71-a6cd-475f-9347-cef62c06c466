@{
    Layout = null;
    var v = "2020-05-18";
}
@using MT.Enterprise.Product.IdentityServer.Models
@model LoginResultDto
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ME-Boost Passport</title>
    <link href="../css/reset.css?_=@v" rel="stylesheet">
    <link href="../css/login.css?_=@v" rel="stylesheet">
</head>

<body>
<div id="particles">
    @if (!string.IsNullOrEmpty(Model.AccessToken) && Model.ExpiresIn > 0)
    {
        <div class="login-form">
            <h3 class="login-form-title" id="loggingin">
            </h3>
        </div>
    }
    else
    {
        <div class="login-form">
            <p class="login-form-title">
                <img src="../images/me_boost_tiny.png" alt="me-boost">
                ME-Boost Passport
            </p>
            <form onsubmit="login(this)">
                <input class="login-form-input" type="text" name="account">
                <input class="login-form-input" type="password" name="password">
                <div class="login-form-actions">
                    <select class="login-language" onchange="switchLanguage(this.value)">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                    </select>
                    <input class="login-form-submit" type="submit" value="">
                </div>
                <p class="error-message"></p>
            </form>
        </div>
    }
</div>
    <script src="../lib/axios.min.js?_=@v"></script>
    <script src="../lib/particles.js?_=@v"></script>
    <script>
        var accessToken = '@Model.AccessToken';
        var expiresIn = @Model.ExpiresIn;
    </script>
    <script src="../js/login.js?_=@v"></script>
    <script src="../js/p.js?_=@v"></script>
</body>

</html>
