using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using IdentityModel.Client;
using IdentityServer4;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Utils.Extensions;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// Cas 登录集成
    /// </summary>
    [Route("movitidentity/casaccount")]
    public class CasAccountController : BaseController
    {
        private readonly string _casLoginUrl;
        private readonly string _casValidateUrl;
        private readonly string _casLogoutUrl;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public CasAccountController(IServiceProvider serviceProvider)
            : base(serviceProvider)
        {
            var casUrl = P_appSettings["CasSettings"]?["CasUrl"]?.ToString();
            _casLoginUrl = casUrl + P_appSettings["CasSettings"]?["Login"];
            _casValidateUrl = casUrl + P_appSettings["CasSettings"]?["Validate"];
            _casLogoutUrl = casUrl + P_appSettings["CasSettings"]?["Logout"];
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("login")]
        public async Task<IActionResult> Login()
        {
            var model = new LoginResultDto();
            string ticket = Request.Query["ticket"];
            string callBackUrl = Request.Query["callBackUrl"];
            string service = HttpUtility.UrlEncode(string.IsNullOrEmpty(P_urlKey) ? $"{P_requestScheme}://{Request.Host.Value}{Request.Path}?callBackUrl={callBackUrl}" : $"{P_requestScheme}://{Request.Host.Value.TrimEnd('/')}/{P_urlKey}/{Request.Path.Value.TrimStart('/')}?callBackUrl={callBackUrl}");

            if (string.IsNullOrEmpty(ticket))
            {
                string redirectUrl = _casLoginUrl + $"?service={service}";
                return Redirect(redirectUrl);
            }

            string validateUrl = _casValidateUrl + $"?ticket={ticket}&service={service}";
            var content = new WebClient().OpenRead(validateUrl);
            if (content != null)
            {
                var reader = new StreamReader(content);
                string resp = await reader.ReadToEndAsync();
                reader.Close();
                var userAccount = resp.Split("\n")[1];

                var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);

                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest(doc.Error);
                }

                if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
                {
                    var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                    var userinfoClient = new UserInfoClient(doc.UserInfoEndpoint);
                    var userinfoResponse = await userinfoClient.GetAsync(accessToken);

                    if (!userinfoResponse.IsError)
                    {
                        model.AccessToken = accessToken;
                        model.ExpiresIn = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.ExpiresIn).ToOurInt();
                    }
                    else
                    {
                        var msg = await Id4Login(model, doc, userAccount);
                        if (!string.IsNullOrEmpty(msg))
                        {
                            return BadRequest(msg);
                        }
                    }
                }
                else
                {
                    var msg = await Id4Login(model, doc, userAccount);
                    if (!string.IsNullOrEmpty(msg))
                    {
                        return BadRequest(msg);
                    }
                }
            }

            return View(model);
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="callBackUrl">地址</param>
        /// <returns>IActionResult</returns>
        [HttpGet("logout")]
        public async Task<IActionResult> Logout(string callBackUrl)
        {
            var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
            {
                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest(doc.Error);
                }

                var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                var revocationClient = new TokenRevocationClient(doc.RevocationEndpoint, "client", "me-boost");
                var revocationResponse = await revocationClient.RevokeAccessTokenAsync(accessToken);

                if (revocationResponse.IsError)
                {
                    return BadRequest(revocationResponse.Error);
                }
            }

            await HttpContext.SignOutAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);

            /*string service = string.IsNullOrEmpty(P_urlKey) ?
                HttpUtility.UrlEncode($"{P_requestScheme}://{Request.Host.Value.TrimEnd('/')}/Login?callBackUrl={callBackUrl}")
                : HttpUtility.UrlEncode($"{P_requestScheme}://{Request.Host.Value.TrimEnd('/')}/{P_urlKey}/Login?callBackUrl={callBackUrl}");*/

            string redirectUrl = _casLogoutUrl; // + $"?service={service}";
            return Redirect(redirectUrl);
        }

        [NonAction]
        private async Task<string> Id4Login(LoginResultDto model, DiscoveryResponse doc, string userAccount)
        {
            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");
            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "account", UserName = userAccount });
            if (tokenResponse.IsError)
            {
                return tokenResponse.Error;
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);

            model.AccessToken = tokenResponse.AccessToken;
            model.ExpiresIn = tokenResponse.ExpiresIn;

            return string.Empty;
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        [NonAction]
        async Task<DiscoveryResponse> GetDiscovery()
        {
            var client = string.IsNullOrEmpty(P_authorityUrl) ?
                new DiscoveryClient($"{P_requestScheme}://{Request.Host.Value}")
                    : new DiscoveryClient(P_authorityUrl);
            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}