var UserLanguageCacheKey = 'user.preferences.lang';

var Locale = {
    zh: {
        account: '账号',
        password: '密码',
        submit: '登 录',
        error: {
            notfound: '账号或密码错误。'
        },
        loggingin: '正在登录中，请稍等！'
    },
    en: {
        account: 'Account',
        password: 'Password',
        submit: 'Sign In',
        error: {
            notfound: 'Account or Password invalid.'
        },
        loggingin: 'Login in progress, please wait'
    }
};

var currentLang = localStorage.getItem(UserLanguageCacheKey);

function getCallBackUrl() {
    var query = location.search
        .replace(/^\?/, '')
        .split('&');
    for (let i = 0; i < query.length; i++) {
        var seg = query[i];
        if (seg.toLowerCase().indexOf('callbackurl=') === 0) {
            return unescape(query[i].split(/callbackurl=/i)[1]);
        }
    }
}

// 没有 return url 则直接返回首页
var callBackUrl = getCallBackUrl();
if (!callBackUrl) {
    location.href = '/';
}

function postback(accessToken, expiresIn) {
    function createFormItem(name, value) {
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        return input;
    }

    var target = document.createElement('form');
    target.action = callBackUrl;
    target.method = 'post';
    target.style.display = 'none';
    target.appendChild(createFormItem('access_token', accessToken));
    target.appendChild(createFormItem('expires_in', expiresIn));
    target.appendChild(createFormItem('lang', currentLang));
    document.body.appendChild(target);
    target.submit();
}

if (accessToken && expiresIn) {
    postback(accessToken, expiresIn);
}

function switchLanguage(lang) {
    if (!lang || !Locale[lang]) {
        return;
    }
    var ln = Locale[lang];
    document.querySelector('input[name="account"]').placeholder = ln.account + ' (admin)';
    document.querySelector('input[name="password"]').placeholder = ln.password + ' (********)';
    document.querySelector('.login-form-submit').value = ln.submit;
    document.querySelector('#loggingin').innerHTML = ln.loggingin;
    localStorage.setItem(UserLanguageCacheKey, lang);
    currentLang = lang;
}

function login(form) {
    var account = form.account.value;
    var password = form.password.value;
    if (account && password) {
        axios
            .post('/account/login', {
                account: account,
                password: password
            })
            .then(function(response) {
                postback(response.data.accessToken, response.data.expiresIn);
            })
            .catch(function(error) {
                var message;
                if (error.response && error.response.status === 400) {
                    message = Locale[currentLang].error.notfound;
                } else {
                    message = (error.response && error.response.data) || error;
                }
                document.querySelector('.error-message').textContent = message;
            });
    }
    window.event.preventDefault();
}

window.onload = function() {
    if (!currentLang) {
        currentLang = 'zh';
    }
    document.querySelector('.login-language').value = currentLang;
    switchLanguage(currentLang);
};
