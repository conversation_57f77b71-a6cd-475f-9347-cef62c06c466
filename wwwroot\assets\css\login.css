canvas {
    display: block;
}

#particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #2c333f;
    background-image: url();
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50% 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.login-form {
    position: absolute;
    width: 420px;
    padding-bottom: 80px;
    pointer-events: none;
}

.login-form-title,
.login-form-input,
.login-form-actions {
    margin-bottom: 32px;
}

.login-form-title {
    font-size: 36px;
    color: #fff;
    display: flex;
    align-items: center;
    user-select: none;
}

    .login-form-title > img {
        width: 42px;
        height: 42px;
        margin: 0 0.3em;
    }

.login-form-input {
    box-sizing: border-box;
    padding: 4px 11px;
    list-style: none;
    position: relative;
    display: inline-block;
    width: 100%;
    height: 40px;
    font-size: 16px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: 0.3s;
    touch-action: manipulation;
    outline: none;
    pointer-events: auto;
}

.login-form-actions {
    display: flex;
    justify-content: space-between;
}

.login-language {
    box-sizing: border-box;
    padding: 4px 11px;
    list-style: none;
    position: relative;
    display: inline-block;
    width: 120px;
    height: 40px;
    font-size: 16px;
    line-height: 1.5;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: 0.3s;
    touch-action: manipulation;
    outline: none;
    pointer-events: auto;
}

.login-form-submit {
    line-height: 1.5;
    display: inline-block;
    text-align: center;
    touch-action: manipulation;
    cursor: pointer;
    background-color: #4882cc;
    border: 1px solid #4882cc;
    white-space: nowrap;
    padding: 0 15px;
    font-size: 16px;
    border-radius: 4px;
    width: 268px;
    height: 40px;
    user-select: none;
    transition: 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    position: relative;
    color: #fff;
    outline: none;
    pointer-events: auto;
}

    .login-form-submit:hover {
        background-color: #6fa0d9;
        border-color: #6fa0d9;
    }

    .login-form-submit:active {
        background-color: #3260a6;
        border-color: #3260a6;
    }

.error-message {
    position: absolute;
    color: #f5222d;
}
