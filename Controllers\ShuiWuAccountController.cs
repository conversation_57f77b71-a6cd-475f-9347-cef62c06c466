﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using IdentityModel.Client;
using IdentityServer4;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Product.IdentityServer.Models.ShuiWu;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// 水务Account
    /// </summary>
    [Route("shuiwuaccount")]
    public class ShuiWuAccountController : Controller
    {
        private readonly JObject _appSettings;
        private readonly string _authUrl;
        private readonly string _internalAuthUrl;
        private readonly string _clientId;
        private readonly string _clienSecret;
        private readonly string _redirect_uri;
        private readonly IDbContext _dbContext;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ShuiWuAccountController(IServiceProvider serviceProvider)
        {
            _appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _dbContext = serviceProvider.GetService<IDbContext>();
            _authUrl = _appSettings["ShuiWuSettings"]["AuthUrl"].ToString();
            _internalAuthUrl = _appSettings["ShuiWuSettings"]["InternalAuthUrl"].ToString();
            _clientId = _appSettings["ShuiWuSettings"]["ClientId"].ToString();
            _clienSecret = _appSettings["ShuiWuSettings"]["ClienSecret"].ToString();
            _redirect_uri = _appSettings["ShuiWuSettings"]["Redirect_uri"].ToString();
        }

        /// <summary>
        /// step1. 获取code
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("login")]
        public async Task<IActionResult> LoginAsync()
        {
            var callBackUrl = Request.Query["callBackUrl"];

            Console.WriteLine($"===================>{DateTime.Now}:callBackUrl is:{callBackUrl}");
            if (callBackUrl.ToString().IndexOf("token") > -1)
            {
                var paramsDict = GetRefererQueryParams(callBackUrl);

                paramsDict.TryGetValue("token", out string token);

                Console.WriteLine($"token is ===>{token}");

                if (!string.IsNullOrWhiteSpace(token))
                {
                    return await HasTokenLogin(token);
                }
            }

            var redirect_uri = HttpUtility.UrlEncode($"{_redirect_uri}?callBackUrl={callBackUrl}");
            var url = $"{_authUrl}/oauth/authorize?response_type=code&scope=server&client_id={_clientId}" +
               $"&redirect_uri={redirect_uri}";
            Console.WriteLine($"get code url is===================>{url}");
            return Redirect(url);
        }

        /// <summary>
        /// ssoCallback
        /// </summary>
        /// <param name="code">code</param>
        /// <returns>登录</returns>
        [HttpGet("sso/callback")]
        public async Task<IActionResult> Login([FromQuery(Name = "code")] string code)
        {
            // eop不传递encode url改为自己抓取
            var queryString = Request.QueryString.ToString();

            // callBackUrl=http://122.112.183.205:31620/customer/auth/sso-callback?referer=/customer/start?bsid=LowCode&btid=SHKSLC&boid=b4825ea8-d8d0-c457-777b-f03bf79208b8&code=gjFfeR&TENANT-ID=0
            var unnecessaryParamIndex = queryString.IndexOf("&code");
            var callbackurl = queryString[..unnecessaryParamIndex].Replace("?callBackUrl=", null);
            Console.WriteLine($"fullcallback is ==>{queryString}");
            var model = new LoginResultDto();
            Console.WriteLine($"===================>0.时间:{DateTime.Now}");
            Console.WriteLine($"===================>1.SSO call back, code is:{code}, callbackurl is:{callbackurl}");
            if (string.IsNullOrEmpty(code))
            {
                Console.WriteLine("SSO服务获取redirectUrl异常，code为空。");
                return BadRequest("SSO服务获取redirectUrl异常，code为空。");
            }

            var accessToken = GetAccessToken(code, callbackurl);
            if (accessToken == null)
            {
                Console.WriteLine($"===================>AccessToken 为空");
                return BadRequest("AccessToken获取失败!");
            }

            Console.WriteLine($"===================>2. token is :{JsonConvert.SerializeObject(accessToken)}");
            var userInfo = accessToken?.Data?.User_info;
            if (userInfo == null)
            {
                return BadRequest("获取用户失败!");
            }

            if (CheckUser(userInfo.Username) == null)
            {
                Console.WriteLine($"{DateTime.Now}找不到账号为{userInfo.Username}的用户");
                return BadRequest($"找不到账号为{userInfo.Username}的用户");
            }

            var doc = await GetDiscovery();

            if (doc.IsError)
            {
                // Console.WriteLine($"===================>id4异常: doc error.");
                return BadRequest($"doc error message: {doc.Error}");
            }

            Console.WriteLine($"===================>doc:{doc}");
            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");

            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "account", UserName = userInfo.Username });

            Console.WriteLine($"===================>userName:{userInfo.Username}");

            Console.WriteLine($"===================>tokenResponse:{tokenResponse.IsError}AccessToken:{tokenResponse.AccessToken}");
            if (tokenResponse.IsError)
            {
                Console.WriteLine($"===================>SSO服务异常，解析用户失败，login name is :{userInfo?.Username}");
                return BadRequest($"账号:{userInfo.Username}解析失败,请联系管理员!");
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };

            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);
            model.AccessToken = tokenResponse.AccessToken;
            model.ExpiresIn = tokenResponse.ExpiresIn;
            Console.WriteLine(model.ToOurJsonString());
            return View(model);
        }

        /// <summary>
        /// 手机端单点
        /// </summary>
        /// <param name="callbackurl">回调地址</param>
        /// <returns>登录</returns>
        [HttpGet("mobile/login")]
        public async Task<IActionResult> MobileLogin([FromQuery(Name = "callbackurl")] string callbackurl)
        {
            var dictQueryParams = GetRefererQueryParams(Request.Query["callBackUrl"]);
            Console.WriteLine($"{DateTime.Now}:callbackurl=================> {callbackurl}");
            var token = dictQueryParams["token"];
            if (string.IsNullOrEmpty(token))
            {
                Console.WriteLine($"token为空url is=================> {callbackurl}");
                return BadRequest("token为空");
            }

            return await HasTokenLogin(token);
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="callBackUrl">地址</param>
        /// <returns>IActionResult</returns>
        [HttpGet("logout")]
        public async Task<IActionResult> Logout(string callBackUrl)
        {
            var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
            {
                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest($"doc error message: {doc.Error}");
                }

                var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                var revocationClient = new TokenRevocationClient(doc.RevocationEndpoint, "client", "me-boost");
                var revocationResponse = await revocationClient.RevokeAccessTokenAsync(accessToken);

                if (revocationResponse.IsError)
                {
                    return BadRequest(revocationResponse.Error);
                }
            }

            await HttpContext.SignOutAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);

            var loginUrl = _authUrl + "/token/login";
            return Redirect(loginUrl);
        }

        private async Task<IActionResult> HasTokenLogin(string token)
        {
            Console.WriteLine($"===================>get access token");
            var url = $"{_internalAuthUrl}/token/user?token={token}";
            Console.WriteLine($"===================>url is:{url}");

            var response = HttpHelper.Get<ShuiWuMobileAccounTokenDto>(url);

            if (response == null || response.Data == null)
            {
                Console.WriteLine($"===================>SSO服务获取Token异常");
                if (!string.IsNullOrEmpty(response?.Message))
                {
                    Console.WriteLine(response.Message);
                }

                return BadRequest($"========>SSO服务获取Token异常!");
            }

            Console.WriteLine($"===================>2. token is :{JsonConvert.SerializeObject(response)}");

            if (string.IsNullOrEmpty(response?.Data?.Username))
            {
                throw new Exception("用户信息为空!");
            }

            if (CheckUser(response.Data.Username) == null)
            {
                Console.WriteLine($"{DateTime.Now}找不到账号为{response.Data.Username}的用户");
                return BadRequest($"找不到账号为{response.Data.Username}的用户");
            }

            var doc = await GetDiscovery();

            if (doc.IsError)
            {
                Console.WriteLine($"===================>id4异常: doc error.");
                return BadRequest($"doc error message: {doc.Error}");
            }

            Console.WriteLine($"===================>doc:{doc}");
            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");
            var userInfo = response.Data;
            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "account", UserName = userInfo.Username });

            Console.WriteLine($"===================>userName:{userInfo.Username}");

            Console.WriteLine($"===================>tokenResponse:{tokenResponse.IsError}AccessToken:{tokenResponse.AccessToken}");
            if (tokenResponse.IsError)
            {
                Console.WriteLine($"===================>SSO服务异常，解析用户失败，login name is :{userInfo?.Username}");
                return BadRequest($"账号:{userInfo.Username}解析失败,请联系管理员!");
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };

            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);

            var model = new LoginResultDto
            {
                AccessToken = tokenResponse.AccessToken,
                ExpiresIn = tokenResponse.ExpiresIn
            };
            return View(model);
        }

        /// <summary>
        /// 用认证服务器带过来的code再去换token
        /// </summary>
        /// <param name="code">code</param>
        /// <param name="callbackurl">callbackurl</param>
        /// <returns>AccessTokenDataDto</returns>
        private ShuiWuAccessTokenDataDto GetAccessToken(string code, string callbackurl)
        {
            Console.WriteLine($"===================>get access token");
            var url = $"{_internalAuthUrl}/token/codeInfo?code={code}&client_id={_clientId}&client_secret={_clienSecret}&redirect_uri={HttpUtility.UrlEncode($"{_redirect_uri}?callBackUrl={callbackurl}")}";
            Console.WriteLine($"===================>url is:{url}");

            var response = HttpHelper.Get<ShuiWuAccessTokenDataDto>(url);

            if (response == null || response.Data == null)
            {
                Console.WriteLine($"===================>SSO服务获取AccessToken异常");
                if (!string.IsNullOrEmpty(response?.Message))
                {
                    Console.WriteLine(response.Message);
                }

                return null;
            }

            return response;
        }

        /// <summary>
        /// 获取callBackUrl引用地址参数
        /// </summary>
        /// <param name="callBackUrl">callBackUrl</param>
        /// <returns>参数</returns>
        private Dictionary<string, string> GetRefererQueryParams(string callBackUrl)
        {
            var dictQueryParams = new Dictionary<string, string>();
            var callBackUri = new Uri(callBackUrl);
            string indexOfValue = "referer=";
            if (callBackUri.Query.IndexOf(indexOfValue) > -1)
            {
                var referer = callBackUri.Query.Substring(callBackUri.Query.IndexOf(indexOfValue) + indexOfValue.Length);
                if (referer.IndexOf("?") > -1)
                {
                    referer = referer.Substring(referer.IndexOf("?") + 1);
                }

                referer.TrimStart('?').Split('&').ToList().ForEach(r =>
                {
                    dictQueryParams[r.Split('=')[0]] = r.Substring(r.Split('=')[0].Length + 1);
                    if (r.Split('=')[0] == "loginid")
                    {
                        dictQueryParams[r.Split('=')[0]] = HttpUtility.UrlDecode(r.Substring(r.Split('=')[0].Length + 1));
                    }
                });
            }

            return dictQueryParams;
        }

        private User CheckUser(string loginId)
        {
            return _dbContext.Client.Queryable<User>().Where(r => r.UserLoginId == loginId).First();
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        private async Task<DiscoveryResponse> GetDiscovery()
        {
            Console.WriteLine($"=====> Discovery scheme is:{Request.Scheme}://{Request.Host.Value}");

            // var client = new DiscoveryClient($"{Request.Scheme}://{Request.Host.Value}");
            // 用真实的域名不行，这个方法不起作用，干脆就写死本地
            var client = new DiscoveryClient("http://127.0.0.1:83");

            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}
