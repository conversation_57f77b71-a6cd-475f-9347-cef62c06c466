using System.ComponentModel.DataAnnotations;

namespace MT.Enterprise.Product.IdentityServer.Models
{
    /// <summary>
    /// 登录Dto
    /// </summary>
    public class LoginDto
    {
        /// <summary>
        /// 账号
        /// </summary>
        /// <value>string</value>
        [Required]
        public string Account { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        /// <value>string</value>
        [Required]
        public string Password { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        /// <value>string</value>
        public string ReturnUrl { get; set; }

        /// <summary>
        /// 是否记住
        /// </summary>
        /// <value>bool</value>
        public bool Remember { get; set; }
    }
}
