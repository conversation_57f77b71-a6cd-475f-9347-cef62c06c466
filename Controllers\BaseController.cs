using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// 控制器基类
    /// </summary>
    public abstract class BaseController : Controller
    {
        private readonly ILogger<BaseController> _log;
        private readonly JObject _appSettings;
        private readonly string _urlKey;
        private readonly string _authorityUrl;
        private readonly string _requestScheme;
        private readonly bool _enableLog;

        /// <summary>
        /// BaseController
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        protected BaseController(IServiceProvider serviceProvider)
        {
            _log = serviceProvider.GetService<ILogger<BaseController>>();
            _appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");

            if (_appSettings["EnableLog"] != null)
            {
                _enableLog = _appSettings["EnableLog"].Value<bool>();
            }

            _urlKey = _appSettings["UrlKey"]?.ToString();
            _authorityUrl = _appSettings["AuthorityUrl"]?.ToString();
            _requestScheme = _appSettings["RequestScheme"]?.ToString();
        }

        /// <summary>
        /// _appSettings
        /// </summary>
        protected JObject P_appSettings => _appSettings;

        /// <summary>
        /// _urlKey
        /// </summary>
        protected string P_urlKey => _urlKey;

        /// <summary>
        /// _authorityUrl
        /// </summary>
        protected string P_authorityUrl => _authorityUrl;

        /// <summary>
        /// _requestScheme
        /// </summary>
        protected string P_requestScheme => _requestScheme;

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">message</param>
        protected void LogInformation(string message)
        {
            if (_enableLog)
            {
                _log.LogWarning(message);
            }
        }
    }
}