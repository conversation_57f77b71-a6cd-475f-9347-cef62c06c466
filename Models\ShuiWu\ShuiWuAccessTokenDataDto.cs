﻿namespace MT.Enterprise.Product.IdentityServer.Models.ShuiWu
{
    /// <summary>
    /// tokenDto
    /// </summary>
    public class ShuiWuAccessTokenDataDto
    {
        /// <summary>
        /// 返回标记.默认值: 1
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 获取认证信息成功
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Data
        /// </summary>
        public DataDto Data { get; set; }

        /// <summary>
        /// Result
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// userInfo
        /// </summary>
        public class User_info
        {
            /// <summary>
            /// id
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// Username
            /// </summary>
            public string Username { get; set; }

            /// <summary>
            /// userType
            /// </summary>
            public string UserType { get; set; }

            /// <summary>
            /// Phone
            /// </summary>
            public string Phone { get; set; }
        }

        /// <summary>
        /// DataDto
        /// </summary>
        public class DataDto
        {
            /// <summary>
            /// Access_token
            /// </summary>
            public string Access_token { get; set; }

            /// <summary>
            /// Token_type
            /// </summary>
            public string Token_type { get; set; }

            /// <summary>
            /// Refresh_token
            /// </summary>
            public string Refresh_token { get; set; }

            /// <summary>
            /// Expires_in
            /// </summary>
            public int Expires_in { get; set; }

            /// <summary>
            /// Scope
            /// </summary>
            public string Scope { get; set; }

            /// <summary>
            /// License
            /// </summary>
            public string License { get; set; }

            /// <summary>
            /// Active
            /// </summary>
            public string Active { get; set; }

            /// <summary>
            /// User_info
            /// </summary>
            public User_info User_info { get; set; }
        }
    }
}
