# deploy_copy 和 deploy_run 是 runner 支持的脚本
# 务必采用架构组提供的 runner 才能使用这两个命令
# 这两个命令分别是将 runner 的文件复制到部署服务器、在部署服务器上运行指定命令

stages:
  - lint_and_build
  - deploy
  - release

before_script:
  - if [[ -z $PROJECT_NAME ]]; then echo 'Missing Secret Variable -> $PROJECT_NAME' && exit 10; fi
  - SOURCE_PUBLIC=https://api.nuget.org/v3/index.json
  - SOURCE_PRIVATE=http://*************:15840/nuget
  - HARBOR=mtbpm.movitech.cn:8858/shuiwu

lint_and_build_task:
  stage: lint_and_build
  except:
    - develop
    - master
    - tags
  script:
    - sed -i -e 's/Warning/Error/g' csharp.ruleset
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet build ./MT.Enterprise.Product.IdentityServer.csproj --no-restore

dev_deploy_task:
  stage: deploy
  only:
    - shuiwu-release
  script:
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet publish ./MT.Enterprise.Product.IdentityServer.csproj --no-restore -o /tmp/publish/$PROJECT_NAME/tmp
    # - dotnet ./CodeObfuscate/Z00bfuscator.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Core.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Application.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.SSO.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.FetchUser.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Entrance.dll
    - tar -cvzf /tmp/$PROJECT_NAME.tgz -C /tmp/publish/$PROJECT_NAME tmp
    - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/shuiwu-project/$PROJECT_NAME
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME && tar -xvzf $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME && rm $PROJECT_NAME.tgz"
    - deploy_run "rm -rf /var/www/shuiwu-project/$PROJECT_NAME/src"
    - deploy_run "mv /var/www/shuiwu-project/$PROJECT_NAME/tmp /var/www/shuiwu-project/$PROJECT_NAME/src"

release_task:
  stage: release
  only:
    - tags
  script:
    # - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    # - dotnet publish ./Medusa.Service.Platform.Entrance/Medusa.Service.Platform.Entrance.csproj --no-restore -o /tmp/releases/$PROJECT_NAME
    # - dotnet ./CodeObfuscate/Z00bfuscator.dll /tmp/releases/$PROJECT_NAME/Medusa.Service.Platform.Core.dll /tmp/releases/$PROJECT_NAME/Medusa.Service.Platform.Application.dll /tmp/releases/$PROJECT_NAME/Medusa.Service.Platform.SSO.dll /tmp/releases/$PROJECT_NAME/Medusa.Service.Platform.FetchUser.dll /tmp/releases/$PROJECT_NAME/Medusa.Service.Platform.Entrance.dll    
    # - echo $CI_COMMIT_TAG > /tmp/releases/$PROJECT_NAME/VERSION
    # - tar -cvzf /tmp/releases/$PROJECT_NAME.tgz -C /tmp/releases $PROJECT_NAME
    # - deploy_run "rm -rf /tmp/releases/$PROJECT_NAME"
    # - deploy_run "if [[ ! -d /tmp/releases ]]; then mkdir -p /tmp/releases; fi"
    # - deploy_copy /tmp/releases/$PROJECT_NAME.tgz /tmp/releases
    - deploy_run "cd /var/www/shuiwu-project/$PROJECT_NAME/src && docker build -t $PROJECT_NAME:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME:$CI_COMMIT_TAG"