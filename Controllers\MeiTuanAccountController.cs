﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using IdentityModel;
using IdentityModel.Client;
using IdentityServer4;
using IdentityServer4.Validation;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// 美团SSO登录
    /// 对接规范详见：TODO：待补充Confluence接口规范
    /// 其它参考文献：https://km.sankuai.com/page/********
    /// </summary>
    [Route("movitidentity/meituanaccount")]
    public class MeiTuanAccountController : BaseController
    {
        private readonly string _host;
        private readonly string _clientId;
        private readonly string _appSecret;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public MeiTuanAccountController(IServiceProvider serviceProvider)
        : base(serviceProvider)
        {
            _host = P_appSettings["MeiTuanSettings"]["AuthUrl"].ToString();
            _clientId = P_appSettings["MeiTuanSettings"]["ClientId"].ToString();
            _appSecret = P_appSettings["MeiTuanSettings"]["Secret"].ToString();
        }

        /// <summary>
        /// step1. 前台(customer、management、mobile...)验证失败后，首先跳转到Identity4拼接美团SSO验证地址，然后跳转到美团SSO去验证，验证通过后再跳到 step2. sso/callback
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("login")]
        public IActionResult Login()
        {
            var meituanSsoUrl = HttpUtility.UrlEncode($"{Request.Scheme}://{Request.Host.Value}/sso/callback?callbackurl=" +
                $"{HttpUtility.UrlEncode(Request.Query["callBackUrl"])}");

            var redirectUrl = $"{_host}/sson/login?client_id={_clientId}&redirect_uri={meituanSsoUrl}";
            return Redirect(redirectUrl);
        }

        /// <summary>
        /// step2. 登录
        /// </summary>
        /// <param name="originalUrl">原始地址，一旦SSO验证成功，那么直接跳转到原始地址</param>
        /// <param name="code">美团验证成功后回跳自带code，用code去换token</param>
        /// <returns>-</returns>
        [HttpGet("sso/callback")]
        public async Task<IActionResult> Login([FromQuery(Name = "callbackurl")] string originalUrl, [FromQuery(Name = "code")] string code)
        {
            var model = new LoginResultDto();

            Console.WriteLine($"1.SSO call back, originalurl is:{originalUrl}, code is:{code}");

            if (string.IsNullOrEmpty(code))
            {
                Console.WriteLine("SSO服务获取Code异常，code为空。");
                return BadRequest("SSO服务获取Code异常，code为空。");
            }

            var accessToken = GetAccessToken(code);
            Console.WriteLine($"2. token is :{JsonConvert.SerializeObject(accessToken)}");
            var user = GetUserInfo(accessToken.AccessToken);
            Console.WriteLine($"3. user is :{JsonConvert.SerializeObject(user)}");

            var doc = await GetDiscovery();

            if (doc.IsError)
            {
                Console.WriteLine($"id4异常: doc error.");
            }

            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");

            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "account", UserName = user.LoginName });

            // await tokenClient.RequestResourceOwnerPasswordAsync(user.LoginName, "empty");
            if (tokenResponse.IsError)
            {
                Console.WriteLine($"SSO服务异常，解析用户失败，login name is :{user?.LoginName}");
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);
            var url = HttpUtility.UrlDecode(originalUrl, Encoding.UTF8);

            Console.Write($"99.url is {url}");

            model.AccessToken = tokenResponse.AccessToken;
            model.ExpiresIn = tokenResponse.ExpiresIn;
            Console.WriteLine($"登录成功: oxxk.");
            return View(model);
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("logout")]
        public IActionResult Logout()
        {
            var meituanSsoUrl = HttpUtility.UrlEncode($"{Request.Scheme}://{Request.Host.Value}/sso/callback?callbackurl=" +
            $"{HttpUtility.UrlEncode(Request.Query["callBackUrl"])}");

            var redirectUrl = $"{_host}/sson/logout?client_id={_clientId}&redirect_uri={meituanSsoUrl}";
            return Redirect(redirectUrl);
        }

        /// <summary>
        /// 用认证服务器带过来的code再去换token
        /// </summary>
        /// <param name="code">认证服务器跳转过来后自带的code</param>
        /// <returns>token信息</returns>
        [NonAction]
        private AccessTokenDataDto GetAccessToken(string code)
        {
            string url = $"{_host}/sson/oauth2.0/access-token";
            Dictionary<string, string> header = new Dictionary<string, string>();
            Dictionary<string, string> signHeader = GetSignHeaders(url, _clientId, _appSecret, "GET");
            header.Add("Date", signHeader["Date"]);
            header.Add("Authorization", signHeader["Authorization"]);
            url += $"?t={new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds()}&code={code}";
            var response = HttpHelper.Get<ResponseAccessDto>(url, new HttpRequestHeaderDto { Headers = header, });

            if (response == null || response.Code != 200)
            {
                Console.WriteLine($"SSO服务获取AccessToken异常, code:{code}.response is:{JsonConvert.SerializeObject(response)}");
                return null;
            }

            return response.Data;
        }

        /// <summary>
        /// 用token去换用户信息
        /// </summary>
        /// <param name="accessToken">accessToken</param>
        /// <returns>用户信息</returns>
        [NonAction]
        private UserInfoDto GetUserInfo(string accessToken)
        {
            string url = $"{_host}/open/api/session/userinfo";
            Dictionary<string, string> header = new Dictionary<string, string>();
            Dictionary<string, string> signHeader = GetSignHeaders(url, _clientId, _appSecret, "POST");
            header.Add("Date", signHeader["Date"]);
            header.Add("Authorization", signHeader["Authorization"]);

            var body = new AccessSendDataDto { AccessToken = accessToken };
            var response = HttpHelper.Post<ResponseUserInfoDto>(url, body, new HttpRequestHeaderDto { Headers = header, });
            if (response == null || response.Code != 200)
            {
                Console.WriteLine($"SSO服务获取用户信息异常, token:{accessToken}.");
                return null;
            }

            return response.Data;
        }

        /// <summary>
        /// 生成签名认证参数Date和Authorization
        /// </summary>
        /// <param name="url">HTTP请求URL</param>
        /// <param name="key">大象公众号的Key</param>
        /// <param name="token">大象公众号的Token</param>
        /// <param name="method">HTTP请求方法，取值包括：PUT、GET、POST、DELETE</param>
        /// <returns>其中携带了Date和Authorization参数</returns>
        [NonAction]
        private Dictionary<string, string> GetSignHeaders(string url, string key, string token, string method)
        {
            if (string.IsNullOrEmpty(token))
            {
                return new Dictionary<string, string>();
            }

            if (string.IsNullOrEmpty(url) || !url.Contains("//"))
            {
                return new Dictionary<string, string>();
            }

            try
            {
                string date = GetDateString();
                string uri = new Uri(url).AbsolutePath;

                var xx = $"{method} {url}\n{date}";
                var yy = $"{method} {url}\\n{date}";

                string temp = method + " " + uri + "\n" + date;
                string signature = GetSignature(temp, token);

                string authorization = $"MWS {key}:{signature}";
                Dictionary<string, string> headers = new Dictionary<string, string>();
                headers.Add("Date", date);
                headers.Add("Authorization", authorization);
                return headers;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成美团签名认证参数失败，参数url:{url},key:{key},secret:{token},method:{method},错误信息:{ex.Message}");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// 按照GMT的时间格式返回时间
        /// </summary>
        /// <returns>时间字符串</returns>
        [NonAction]
        private string GetDateString()
        {
            return DateTime.UtcNow.ToString("r");
        }

        /// <summary>
        /// 采用HMACSHA1加密生成签名
        /// </summary>
        /// <param name="data">信息</param>
        /// <param name="secret">key</param>
        /// <returns>加密信息</returns>
        [NonAction]
        private string GetSignature(string data, string secret)
        {
            // HMACSHA1加密
            HMACSHA1 hmacsha1 = new HMACSHA1();
            hmacsha1.Key = System.Text.Encoding.UTF8.GetBytes(secret);

            byte[] dataBuffer = System.Text.Encoding.UTF8.GetBytes(data);
            byte[] hashBytes = hmacsha1.ComputeHash(dataBuffer);
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        [NonAction]
        async Task<DiscoveryResponse> GetDiscovery()
        {
            var client = new DiscoveryClient($"{Request.Scheme}://{Request.Host.Value}");
            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}
