function getCallBackUrl() {
    var query = location.search
        .replace(/^\?/, '');
    if (query.indexOf('callBackUrl=') === 0) {
        
        // 去除多余string防止因参数过多导致地址404
        var codeIndex = query.indexOf('&code');
        var url = codeIndex > 0 ? query.substring(0, codeIndex).replace('callBackUrl=', '') : query.replace('callBackUrl=', '');
        return unescape(url);
    }
}

// 没有 return url 则直接返回首页
var callBackUrl = getCallBackUrl();
if (!callBackUrl) {
    location.href = '/';
}

function postback(accessToken, expiresIn) {
    function createFormItem(name, value) {
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        return input;
    }

    var target = document.createElement('form');
    target.action = callBackUrl;
    target.method = 'post';
    target.style.display = 'none';
    target.appendChild(createFormItem('access_token', accessToken));
    target.appendChild(createFormItem('expires_in', expiresIn));
    target.appendChild(createFormItem('lang', 'zh'));
    document.body.appendChild(target);
    target.submit();
}

if (accessToken && expiresIn) {
    postback(accessToken, expiresIn);
}
