﻿namespace MT.Enterprise.Product.IdentityServer.Models
{
    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInfoDto
    {
        /// <summary>
        /// 用户id，等价于ORG的empid，用户不可变的唯一标识，下游如果落库标识用户，请使用该字段
        /// </summary>
        public long Uid { get; set; }

        /// <summary>
        /// 登录帐号，mis号，可能发生变化
        /// </summary>
        public string LoginName { get; set; }

        /// <summary>
        /// 姓名，可能发生变化
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 工号，PS系统里的员工号，非美团员工工号无意义
        /// </summary>
        public string StaffId { get; set; }

        /// <summary>
        /// 租户id，企业ID，美团默认为1
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// 邮箱，可能发生变化
        /// </summary>
        public string Email { get; set; }
    }
}