﻿namespace MT.Enterprise.Product.IdentityServer.Models
{
    /// <summary>
    /// AccessToken返回结果结构
    /// </summary>
    public class AccessTokenDataDto
    {
        /// <summary>
        /// accessToken 登录会话，即 ssoid
        /// </summary>
        public string AccessToken { get; set; }

        /// <summary>
        /// token 过期时间，单位秒
        /// </summary>
        public string Expires { get; set; }

        /// <summary>
        /// 用于刷新会话的 token
        /// </summary>
        public string RefreshToken { get; set; }

        /// <summary>
        /// refreshToken 过期时间，单位秒
        /// </summary>
        public string RefreshExpires { get; set; }
    }
}
