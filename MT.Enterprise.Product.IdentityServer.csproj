﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <CodeAnalysisRuleSet>csharp.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DocumentationFile>bin\Debug\netcoreapp2.1\MT.Enterprise.Product.IdentityServer.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="Views\Home\" />
    <Folder Include="Views\Account\" />
    <Folder Include="wwwroot\" />
    <Folder Include="wwwroot\assets\" />
    <Folder Include="wwwroot\assets\css\" />
    <Folder Include="wwwroot\assets\images\" />
    <Folder Include="wwwroot\assets\js\" />
    <Folder Include="wwwroot\assets\lib\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="IdentityServer4" Version="2.2.0" />
    <PackageReference Include="IdentityServer4.AspNetIdentity" Version="2.1.0" />
    <PackageReference Include="Novell.Directory.Ldap.NETStandard2_0" Version="3.1.0" />
    <PackageReference Include="nacos-sdk-csharp.AspNetCore" Version="1.3.5" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="wwwroot\assets\" />
    <Content Remove="wwwroot\assets\css\" />
    <Content Remove="wwwroot\assets\images\" />
    <Content Remove="wwwroot\assets\js\" />
    <Content Remove="wwwroot\assets\lib\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Core\MT.Enterprise.Core.csproj" />
    <ProjectReference Include="..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Utils\MT.Enterprise.Utils.csproj" />
  </ItemGroup>
</Project>
