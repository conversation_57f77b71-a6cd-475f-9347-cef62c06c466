using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityModel;
using IdentityModel.Client;
using IdentityServer4;
using IdentityServer4.Validation;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// 钉钉 登录集成
    /// </summary>
    [Route("movitidentity/ddaccount")]
    public class DdAccountController : BaseController
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public DdAccountController(IServiceProvider serviceProvider)
        : base(serviceProvider)
        {
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("login")]
        public async Task<IActionResult> Login()
        {
            if (P_appSettings["DdSettings"]?["Enable"]?.ToString().ToLower() != "true")
            {
                throw new Exception("钉钉授权登录没用开启");
            }

            // 基于浏览器的验证
            var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            var model = new LoginResultDto();

            if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
            {
                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest(doc.Error);
                }

                var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                var userinfoClient = new UserInfoClient(doc.UserInfoEndpoint);
                var userinfoResponse = await userinfoClient.GetAsync(accessToken);

                if (!userinfoResponse.IsError)
                {
                    model.AccessToken = accessToken;
                    model.ExpiresIn = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.ExpiresIn).ToOurInt();
                }
            }

            return View(model);
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="model">钉钉code</param>
        /// <returns>IActionResult</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] DdLoginDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest("Invalid code.");
            }

            var doc = await GetDiscovery();

            if (doc.IsError)
            {
                return BadRequest(doc.Error);
            }

            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");
            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Dd",  extra: new { model.Code });

            if (tokenResponse.IsError)
            {
                return BadRequest(tokenResponse.Error);
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);

            return Ok(new LoginResultDto { AccessToken = tokenResponse.AccessToken, ExpiresIn = tokenResponse.ExpiresIn });
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        [NonAction]
        async Task<DiscoveryResponse> GetDiscovery()
        {
            var client = new DiscoveryClient($"{Request.Scheme}://{Request.Host.Value}");
            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}
