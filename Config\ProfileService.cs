using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityServer4.Extensions;
using IdentityServer4.Models;
using IdentityServer4.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using Newtonsoft.Json.Linq;

namespace MT.Enterprise.Product.IdentityServer.Config
{
    /// <summary>
    /// ProfileService
    /// </summary>
    public class ProfileService : IProfileService
    {
        private readonly IDbContext _dbContext;
        private readonly IMemoryCache _memoryCache;
        private readonly string _ssoType;

        /// <summary>
        /// Initializes a new instance of the <see cref="ProfileService"/> class.
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ProfileService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<IDbContext>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _memoryCache = serviceProvider.GetService<IMemoryCache>();
            _ssoType = appSettings["SSOType"]?.ToString();
        }

        /// <summary>
        /// GetProfileDataAsync
        /// </summary>
        /// <param name="context">上下文</param>
        /// <returns>Task</returns>
        public Task GetProfileDataAsync(ProfileDataRequestContext context)
        {
            (bool active, List<Claim> claims) = GetClaims(context.Subject.GetSubjectId());
            context.IssuedClaims = claims;
            return Task.CompletedTask;
        }

        /// <summary>
        /// IsActiveAsync
        /// </summary>
        /// <param name="context">上下文</param>
        /// <returns>Task</returns>
        public Task IsActiveAsync(IsActiveContext context)
        {
            (bool active, List<Claim> claims) = GetClaims(context.Subject.GetSubjectId());
            context.IsActive = active;
            return Task.CompletedTask;
        }

        (bool active, List<Claim> claims) GetClaims(string account)
        {
            if (!_memoryCache.TryGetValue(account.ToLower(), out User cacheUser))
            {
                switch (_ssoType)
                {
                    case "user":
                        cacheUser = _dbContext.Client.Queryable<User>().First(t => t.UserLoginId == account);
                        if (cacheUser.Status == 1)
                        {
                            _memoryCache.Set(account.ToLower(), cacheUser, TimeSpan.FromSeconds(15));
                        }
                        else
                        {
                            cacheUser = null;
                        }

                        break;
                    default:
                        cacheUser = new User { UserLoginId = account };
                        _memoryCache.Set(account.ToLower(), cacheUser, TimeSpan.FromSeconds(15));
                        break;
                }
            }

            if (cacheUser != null)
            {
                switch (_ssoType)
                {
                    case "user":
                        return (true, new List<Claim>
                        {
                            new Claim("account", cacheUser.UserLoginId),
                            new Claim(IdentityModel.JwtClaimTypes.Name, cacheUser.UserName),
                            new Claim(IdentityModel.JwtClaimTypes.Gender, cacheUser.Gender ?? string.Empty),
                            new Claim(IdentityModel.JwtClaimTypes.Email, cacheUser.Email ?? string.Empty),
                            new Claim(IdentityModel.JwtClaimTypes.PhoneNumber, cacheUser.MobilePhone ?? string.Empty),
                        });
                    default:
                        return (true, new List<Claim>
                        {
                            new Claim("account", cacheUser.UserLoginId)
                        });
                }
            }

            return (false, null);
        }
    }
}
