using System.Collections.Generic;
using IdentityServer4.Models;

namespace MT.Enterprise.Product.IdentityServer.Config
{
    /// <summary>
    /// IdentityResourcesStore
    /// </summary>
    public static class IdentityResourcesStore
    {
        /// <summary>
        /// Get
        /// </summary>
        /// <returns>IdentityResource</returns>
        public static IEnumerable<IdentityResource> Get()
        {
            return new List<IdentityResource>
            {
                new IdentityResources.OpenId(),
                new IdentityResources.Profile()
            };
        }
    }
}
