﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "UrlKey": "sso",
  "RequestScheme": "http",
  "AuthorityUrl": "",
  "SSOType": "account",
  "EnableLog": true,
  "LdapSettings": {
    "Enable": true,
    "UseSSL": false,
    "ServerName": "movit-tech.com",
    "ServerPort": 389
  },
  "DdSettings": {
    "Enable": true,
    "ApiUrl": "https://oapi.dingtalk.com",
    "AppKey": "",
    "AppSecret": "zVg9dJvcMEkkhN8XyK4WcW_kph_iXevekRFJOToG_P6wWd4qqukIF-RJmoeUo4R_"
  },
  "Persistence": {
    "Boost": {
      "DbType": "Mysql",
      "ConnectionString": "server=*************;Database=Boost;uid=bpm;Pwd=****************"
    }
  },
  "SaiWuSettings": {
    "ClientId": "bpm",
    "ClienSecret": "bpm",
    "AuthUrl": "http://***********:1011",
    "Redirect_uri": "http://***************:32014/saiwuaccount/sso/callback"
  },
  "ShuiWuSettings": {
    "ClientId": "bpm",
    "ClienSecret": "bpm",
    "AuthUrl": "http://**************:2006",
    "InternalAuthUrl": "http://**************:2006",
    "Redirect_uri": "https://*************:2005/identity/shuiwuaccount/sso/callback"
  }
}
