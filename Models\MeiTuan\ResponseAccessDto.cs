﻿namespace MT.Enterprise.Product.IdentityServer.Models
{
    /// <summary>
    /// AccessToken的返回结构
    /// </summary>
    public class ResponseAccessDto
    {
        /// <summary>
        /// 返回结果状态：200成功 非200失败
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// dataObject
        /// </summary>
        public AccessTokenDataDto Data { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Msg { get; set; }
    }
}
