﻿@{
    Layout = null;
    var v = "2023-02-22";
}

@using MT.Enterprise.Product.IdentityServer.Models
@model LoginResultDto
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ME-Boost Passport</title>
    <link href="/assets/css/reset.css?_=@v" rel="stylesheet">
    <link href="/assets/css/login.css?_=@v" rel="stylesheet">
</head>

<body>
    <script src="/assets/lib/axios.min.js?_=@v"></script>
    <script src="/assets/lib/particles.js?_=@v"></script>
    <script>
        var accessToken = '@Model.AccessToken';
        var expiresIn = @Model.ExpiresIn;
    </script>
    <script src="/assets/js/caslogin.js?_=@v"></script>
    <script src="/assets/js/p.js?_=@v"></script>
</body>
</html>
