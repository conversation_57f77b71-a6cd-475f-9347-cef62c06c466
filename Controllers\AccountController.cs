using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityModel.Client;
using IdentityServer4;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Utils.Extensions;

namespace MT.Enterprise.Product.IdentityServer.Controllers
{
    /// <summary>
    /// AccountController
    /// </summary>
    [Route("movitidentity/account")]
    public class AccountController : Controller
    {
        /// <summary>
        /// 账号密码 登录
        /// </summary>
        /// <returns>IActionResult</returns>
        [HttpGet("login")]
        public async Task<IActionResult> Login()
        {
            // 基于浏览器的验证
            var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            var model = new LoginResultDto();

            if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
            {
                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest(doc.Error);
                }

                var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                var userinfoClient = new UserInfoClient(doc.UserInfoEndpoint);
                var userinfoResponse = await userinfoClient.GetAsync(accessToken);

                if (!userinfoResponse.IsError)
                {
                    model.AccessToken = accessToken;
                    model.ExpiresIn = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.ExpiresIn).ToOurInt();
                }
            }

            return View(model);
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="model">账号信息</param>
        /// <returns>IActionResult</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody]LoginDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest("Invalid account or password.");
            }

            var doc = await GetDiscovery();

            if (doc.IsError)
            {
                return BadRequest(doc.Error);
            }

            var tokenClient = new TokenClient(doc.TokenEndpoint, "client", "me-boost");
            var tokenResponse = await tokenClient.RequestCustomGrantAsync("Movitech", extra: new { ValidateType = "form", UserName = model.Account, Password = model.Password });

            if (tokenResponse.IsError)
            {
                return BadRequest(tokenResponse.Error);
            }

            var claims = new[]
            {
                new Claim(OpenIdConnectParameterNames.AccessToken, tokenResponse.AccessToken),
                new Claim(OpenIdConnectParameterNames.ExpiresIn, tokenResponse.ExpiresIn.ToString()),
            };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme));
            var props = new AuthenticationProperties
            {
                IsPersistent = true
            };

            await HttpContext.SignInAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme, principal, props);

            return Ok(new LoginResultDto { AccessToken = tokenResponse.AccessToken, ExpiresIn = tokenResponse.ExpiresIn });
        }

        /// <summary>
        /// 登出
        /// </summary>
        /// <param name="callBackUrl">地址</param>
        /// <returns>IActionResult</returns>
        [HttpGet("logout")]
        public async Task<IActionResult> Logout(string callBackUrl)
        {
            var auth = await HttpContext.AuthenticateAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            if (auth.Succeeded && auth.Principal?.Claims?.Count() > 0)
            {
                var doc = await GetDiscovery();

                if (doc.IsError)
                {
                    return BadRequest(doc.Error);
                }

                var accessToken = auth.Principal.FindFirstValue(OpenIdConnectParameterNames.AccessToken);

                var revocationClient = new TokenRevocationClient(doc.RevocationEndpoint, "client", "me-boost");
                var revocationResponse = await revocationClient.RevokeAccessTokenAsync(accessToken);

                if (revocationResponse.IsError)
                {
                    return BadRequest(revocationResponse.Error);
                }
            }

            await HttpContext.SignOutAsync(IdentityServerConstants.DefaultCookieAuthenticationScheme);
            return RedirectToAction("Login", "Account", new { callBackUrl });
        }

        /// <summary>
        /// GetDiscovery
        /// </summary>
        /// <returns>DiscoveryResponse</returns>
        [NonAction]
        async Task<DiscoveryResponse> GetDiscovery()
        {
            var client = new DiscoveryClient($"{Request.Scheme}://{Request.Host.Value}");
            client.Policy.RequireHttps = false;

            return await client.GetAsync();
        }
    }
}
