using System;
using System.Security.Claims;
using System.Threading.Tasks;
using IdentityModel;
using IdentityServer4.Models;
using IdentityServer4.Validation;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.Product.IdentityServer.Models;
using MT.Enterprise.Utils;
using Newtonsoft.Json.Linq;
using Novell.Directory.Ldap;

namespace MT.Enterprise.Product.IdentityServer.Config
{
    /*/// <summary>
    /// ResourceOwnerPasswordValidator 作废
    /// </summary>
    public class ResourceOwnerPasswordValidator : IResourceOwnerPasswordValidator
    {
        private readonly IDbContext _dbContext;
        private readonly LdapSettings _ldapSettings;
        private readonly IMemoryCache _memoryCache;
        private readonly string _ssoType;

        /// <summary>
        /// Initializes a new instance of the <see cref="ResourceOwnerPasswordValidator"/> class.
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ResourceOwnerPasswordValidator(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<IDbContext>();
            _memoryCache = serviceProvider.GetService<IMemoryCache>();

            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");

            _ssoType = appSettings["SSOType"]?.ToString();

            _ldapSettings = new LdapSettings();
            bool.TryParse(appSettings["LdapSettings"]?["UseSSL"]?.ToString(), out bool useSSL);
            int.TryParse(appSettings["LdapSettings"]?["ServerPort"]?.ToString(), out int port);
            _ldapSettings.UseSSL = useSSL;
            _ldapSettings.ServerName = appSettings["LdapSettings"]?["ServerName"]?.ToString();
            _ldapSettings.ServerPort = port;
        }

        /// <summary>
        /// ValidateAsync
        /// </summary>
        /// <param name="context">上下文</param>
        /// <returns>Task</returns>
        public Task ValidateAsync(ResourceOwnerPasswordValidationContext context)
        {
            User user;
            switch (_ssoType)
            {
                case "cas":
                    user = new User
                    {
                        UserLoginId = context.UserName
                    };

                    if (!_memoryCache.TryGetValue(context.UserName.ToLower(), out User _))
                    {
                        _memoryCache.Set(context.UserName.ToLower(), user, TimeSpan.FromSeconds(15));
                    }

                    context.Result = new GrantValidationResult(user.UserLoginId, OidcConstants.AuthenticationMethods.Password);
                    break;
                case "ad":
                    if (LadpAuthenticate(context.UserName, context.Password))
                    {
                        user = new User
                        {
                            UserLoginId = context.UserName
                        };

                        if (!_memoryCache.TryGetValue(context.UserName.ToLower(), out User _))
                        {
                            _memoryCache.Set(context.UserName.ToLower(), user, TimeSpan.FromSeconds(15));
                        }

                        context.Result = new GrantValidationResult(user.UserLoginId, OidcConstants.AuthenticationMethods.Password);
                    }
                    else
                    {
                        context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "invalid credential");
                    }

                    break;
                case "form":
                    var query = _dbContext.Client.Queryable<User>().Where(it => it.UserLoginId == context.UserName && it.Password == Crypto.SHA1(context.Password) && it.Status == 1);
                    if (query.Any())
                    {
                        user = query.First();
                        if (!_memoryCache.TryGetValue(context.UserName.ToLower(), out User _))
                        {
                            _memoryCache.Set(context.UserName.ToLower(), user, TimeSpan.FromSeconds(15));
                        }

                        context.Result = new GrantValidationResult(user.UserLoginId, OidcConstants.AuthenticationMethods.Password);
                    }
                    else
                    {
                        context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "invalid credential");
                    }

                    break;
                case "account":
                    var accountQuery = _dbContext.Client.Queryable<User>().Where(q => q.UserLoginId == context.UserName && q.Status == 1);
                    if (accountQuery.Any())
                    {
                        user = accountQuery.First();
                        if (!_memoryCache.TryGetValue(context.UserName.ToLower(), out User cacheUser))
                        {
                            _memoryCache.Set(context.UserName.ToLower(), user, TimeSpan.FromSeconds(15));
                        }

                        context.Result = new GrantValidationResult(user.UserLoginId, OidcConstants.AuthenticationMethods.Password);
                    }
                    else
                    {
                        context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "invalid credential");
                    }

                    break;
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// 域登录
        /// </summary>
        /// <param name="userName">域账号</param>
        /// <param name="passWord">域密码</param>
        /// <returns>true:登录成功, false:登录失败</returns>
        private bool LadpAuthenticate(string userName, string passWord)
        {
            using (var ldapConnection = new LdapConnection() { SecureSocketLayer = _ldapSettings.UseSSL })
            {
                ldapConnection.Connect(_ldapSettings.ServerName, _ldapSettings.ServerPort);

                try
                {
                    ldapConnection.Bind($"{userName}@{_ldapSettings.ServerName}", passWord);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
        }
    }*/
}
